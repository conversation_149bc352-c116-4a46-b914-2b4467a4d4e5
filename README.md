# Karma
Karma is a machine learning necto level bot in rlgym ppo. This bot was trained and created by me (kuen<PERSON>).

## INSTALLATION
This repo made by zealan will cover everything from installaing dependencies to creating and making your own bot: [Zealan Guide](https://github.com/ZealanL/RLGym-PPO-Guide/blob/main/intro.md).

## Visualizer
My example.py already has code implemented in it to use Zealans 'RocketSimVis' so you will be able to see how your bot plays in a virtually created environment while it trains. This is not needed but if you want to see your bot play you can download it here: [Zealan Guide](https://github.com/ZealanL/RocketSimVis).
