2025-07-21 02:04:41,130 INFO    MainThread:22852 [wandb_setup.py:_flush():80] Current SDK version is 0.21.0
2025-07-21 02:04:41,130 INFO    MainThread:22852 [wandb_setup.py:_flush():80] Configure stats pid to 22852
2025-07-21 02:04:41,130 INFO    MainThread:22852 [wandb_setup.py:_flush():80] Loading settings from C:\Users\<USER>\.config\wandb\settings
2025-07-21 02:04:41,130 INFO    MainThread:22852 [wandb_setup.py:_flush():80] Loading settings from C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\wandb\settings
2025-07-21 02:04:41,130 INFO    MainThread:22852 [wandb_setup.py:_flush():80] Loading settings from environment variables
2025-07-21 02:04:41,130 INFO    MainThread:22852 [wandb_init.py:setup_run_log_directory():703] Logging user logs to C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\wandb\run-20250721_020441-bjoutfww\logs\debug.log
2025-07-21 02:04:41,130 INFO    MainThread:22852 [wandb_init.py:setup_run_log_directory():704] Logging internal logs to C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\wandb\run-20250721_020441-bjoutfww\logs\debug-internal.log
2025-07-21 02:04:41,130 INFO    MainThread:22852 [wandb_init.py:init():830] calling init triggers
2025-07-21 02:04:41,130 INFO    MainThread:22852 [wandb_init.py:init():835] wandb.init called with sweep_config: {}
config: {'n_proc': 32, 'min_inference_size': 29, 'timestep_limit': 100000000000, 'exp_buffer_size': 150000, 'ts_per_iteration': 50000, 'standardize_returns': True, 'standardize_obs': False, 'policy_layer_sizes': [256, 256, 256], 'critic_layer_sizes': [256, 256, 256], 'ppo_epochs': 2, 'ppo_batch_size': 50000, 'ppo_minibatch_size': 25000, 'ppo_ent_coef': 0.01, 'ppo_clip_range': 0.1, 'gae_lambda': 0.95, 'gae_gamma': 0.99, 'policy_lr': 0.0002, 'critic_lr': 0.0002, 'shm_buffer_size': 8192, '_wandb': {}}
2025-07-21 02:04:41,130 INFO    MainThread:22852 [wandb_init.py:init():871] starting backend
2025-07-21 02:04:41,346 INFO    MainThread:22852 [wandb_init.py:init():874] sending inform_init request
2025-07-21 02:04:41,356 INFO    MainThread:22852 [wandb_init.py:init():882] backend started and connected
2025-07-21 02:04:41,359 INFO    MainThread:22852 [wandb_init.py:init():953] updated telemetry
2025-07-21 02:04:41,361 INFO    MainThread:22852 [wandb_init.py:init():977] communicating run to backend with 90.0 second timeout
2025-07-21 02:04:41,858 INFO    MainThread:22852 [wandb_init.py:init():1024] run resumed
2025-07-21 02:04:41,858 INFO    MainThread:22852 [wandb_init.py:init():1029] starting run threads in backend
2025-07-21 02:04:41,913 INFO    MainThread:22852 [wandb_run.py:_console_start():2458] atexit reg
2025-07-21 02:04:41,913 INFO    MainThread:22852 [wandb_run.py:_redirect():2306] redirect: wrap_raw
2025-07-21 02:04:41,913 INFO    MainThread:22852 [wandb_run.py:_redirect():2375] Wrapping output streams.
2025-07-21 02:04:41,913 INFO    MainThread:22852 [wandb_run.py:_redirect():2398] Redirects installed.
2025-07-21 02:04:41,915 INFO    MainThread:22852 [wandb_init.py:init():1075] run started, returning control to user process
2025-07-21 02:13:11,198 INFO    MainThread:22852 [wandb_run.py:_finish():2224] finishing run xzskullyxz-rlgym/rlgym-ppo/bjoutfww
2025-07-21 02:13:11,198 INFO    MainThread:22852 [wandb_run.py:_atexit_cleanup():2423] got exitcode: 0
2025-07-21 02:13:11,199 INFO    MainThread:22852 [wandb_run.py:_restore():2405] restore
2025-07-21 02:13:11,199 INFO    MainThread:22852 [wandb_run.py:_restore():2411] restore done
2025-07-21 02:13:11,852 INFO    MainThread:22852 [wandb_run.py:_footer_history_summary_info():3903] rendering history
2025-07-21 02:13:11,852 INFO    MainThread:22852 [wandb_run.py:_footer_history_summary_info():3935] rendering summary
2025-07-21 02:13:11,853 INFO    MainThread:22852 [wandb_run.py:_footer_sync_info():3864] logging synced files
