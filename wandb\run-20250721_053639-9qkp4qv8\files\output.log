Checkpoint loaded!
<PERSON><PERSON> successfully initialized!
Press (p) to pause (c) to checkpoint, (q) to checkpoint and quit (after next iteration)
C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\torch\optim\adam.py:534: UserWarning: The operator 'aten::lerp.Scalar_out' is not currently supported on the DML backend and will fall back to run on the CPU. This may have performance implications. (Triggered internally at C:\__w\1\s\pytorch-directml-plugin\torch_directml\csrc\dml\dml_cpu_fallback.cpp:17.)
  torch._foreach_lerp_(device_exp_avgs, device_grads, 1 - beta1)

--------BEGIN ITERATION REPORT--------
Policy Reward: 94.85118
Policy Entropy: 4.49912
Value Function Loss: nan

Mean KL Divergence: 0.00002
SB3 Clip Fraction: 0.00012
Policy Update Magnitude: 0.33629
Value Function Update Magnitude: 0.34427

Collected Steps per Second: 11,245.39616
Overall Steps per Second: 7,351.23295

Timestep Collection Time: 4.44982
Timestep Consumption Time: 2.35720
PPO Batch Consumption Time: 0.54607
Total Iteration Time: 6.80702

Cumulative Model Updates: 2
Cumulative Timesteps: 100,050

Timesteps Collected: 50,040
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 87.97811
Policy Entropy: 4.49860
Value Function Loss: 0.26452

Mean KL Divergence: 0.00029
SB3 Clip Fraction: 0.00083
Policy Update Magnitude: 0.44089
Value Function Update Magnitude: 0.42175

Collected Steps per Second: 10,573.89837
Overall Steps per Second: 7,655.82799

Timestep Collection Time: 4.72995
Timestep Consumption Time: 1.80285
PPO Batch Consumption Time: 0.16072
Total Iteration Time: 6.53280

Cumulative Model Updates: 6
Cumulative Timesteps: 150,064

Timesteps Collected: 50,014
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 143.43212
Policy Entropy: 4.49700
Value Function Loss: 0.29985

Mean KL Divergence: 0.00111
SB3 Clip Fraction: 0.03934
Policy Update Magnitude: 0.48992
Value Function Update Magnitude: 0.49265

Collected Steps per Second: 9,651.14724
Overall Steps per Second: 6,912.26094

Timestep Collection Time: 5.18115
Timestep Consumption Time: 2.05296
PPO Batch Consumption Time: 0.15507
Total Iteration Time: 7.23410

Cumulative Model Updates: 12
Cumulative Timesteps: 200,068

Timesteps Collected: 50,004
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 81.45547
Policy Entropy: 4.49605
Value Function Loss: 0.39751

Mean KL Divergence: 0.00104
SB3 Clip Fraction: 0.02912
Policy Update Magnitude: 0.42413
Value Function Update Magnitude: 0.42102

Collected Steps per Second: 11,562.97307
Overall Steps per Second: 7,870.34924

Timestep Collection Time: 4.32570
Timestep Consumption Time: 2.02954
PPO Batch Consumption Time: 0.15383
Total Iteration Time: 6.35525

Cumulative Model Updates: 18
Cumulative Timesteps: 250,086

Timesteps Collected: 50,018
--------END ITERATION REPORT--------


Saving checkpoint 250086...
Checkpoint 250086 saved!
