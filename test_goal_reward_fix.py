#!/usr/bin/env python3
"""
Test script to verify the fixed GoalSpeedAndPlacementReward works correctly.
"""

import numpy as np
from customreward import GoalSpeedAndPlacementReward
from rlgym_sim.utils.gamestates import GameState, PlayerData, PhysicsObject
from rlgym_sim.utils.common_values import BLUE_TEAM, ORANGE_TEAM, BALL_MAX_SPEED, BALL_RADIUS

def create_mock_game_state(blue_score=0, orange_score=0, ball_velocity=None, ball_position=None):
    """Create a mock game state for testing."""
    state = GameState()
    state.blue_score = blue_score
    state.orange_score = orange_score
    
    # Create ball
    state.ball = PhysicsObject()
    if ball_velocity is not None:
        state.ball.linear_velocity = np.array(ball_velocity)
    else:
        state.ball.linear_velocity = np.array([0.0, 0.0, 0.0])
    
    if ball_position is not None:
        state.ball.position = np.array(ball_position)
    else:
        state.ball.position = np.array([0.0, 0.0, BALL_RADIUS])
    
    return state

def create_mock_player(team_num=BLUE_TEAM, car_id=0, ball_touched=False):
    """Create a mock player for testing."""
    player = PlayerData()
    player.team_num = team_num
    player.car_id = car_id
    player.ball_touched = ball_touched
    return player

def test_goal_speed_reward():
    """Test that the reward correctly calculates speed-based rewards for goals."""
    print("=== Testing Goal Speed Reward ===")
    
    reward_func = GoalSpeedAndPlacementReward(speed_weight=1.0, height_weight=1.0)
    
    # Create initial state
    initial_state = create_mock_game_state(blue_score=0, orange_score=0)
    reward_func.reset(initial_state)
    
    # Create player
    player = create_mock_player(team_num=BLUE_TEAM, car_id=0)
    
    # Step 1: Player touches ball with high velocity
    ball_velocity = [1000.0, 500.0, 200.0]  # Fast shot
    state1 = create_mock_game_state(blue_score=0, orange_score=0, ball_velocity=ball_velocity)
    player.ball_touched = True
    
    reward1 = reward_func.get_reward(player, state1, np.array([0]))
    print(f"✓ Ball touch reward: {reward1} (should be 0)")
    
    # Step 2: Goal is scored (blue team scores)
    player.ball_touched = False  # No longer touching
    state2 = create_mock_game_state(blue_score=1, orange_score=0, ball_velocity=[0, 0, 0])
    
    reward2 = reward_func.get_reward(player, state2, np.array([0]))
    expected_speed = np.linalg.norm(ball_velocity) / BALL_MAX_SPEED
    print(f"✓ Goal reward: {reward2:.4f} (expected: ~{expected_speed:.4f})")
    
    return abs(reward2 - expected_speed) < 0.01

def test_goal_placement_reward():
    """Test that the reward correctly applies height bonuses."""
    print("\n=== Testing Goal Placement Reward ===")
    
    reward_func = GoalSpeedAndPlacementReward(speed_weight=1.0, height_weight=2.0, min_height=200)
    
    # Test high shot
    initial_state = create_mock_game_state(blue_score=0, orange_score=0)
    reward_func.reset(initial_state)
    
    player = create_mock_player(team_num=BLUE_TEAM, car_id=0)
    
    # High ball shot
    ball_velocity = [500.0, 0.0, 0.0]
    ball_position = [0.0, 0.0, 300.0]  # Above min_height
    state1 = create_mock_game_state(blue_score=0, orange_score=0, 
                                   ball_velocity=ball_velocity, ball_position=ball_position)
    player.ball_touched = True
    reward_func.get_reward(player, state1, np.array([0]))
    
    # Goal scored
    player.ball_touched = False
    state2 = create_mock_game_state(blue_score=1, orange_score=0)
    reward_high = reward_func.get_reward(player, state2, np.array([0]))
    
    # Test low shot
    reward_func.reset(initial_state)
    ball_position_low = [0.0, 0.0, 100.0]  # Below min_height
    state1_low = create_mock_game_state(blue_score=0, orange_score=0,
                                       ball_velocity=ball_velocity, ball_position=ball_position_low)
    player.ball_touched = True
    reward_func.get_reward(player, state1_low, np.array([0]))
    
    player.ball_touched = False
    state2_low = create_mock_game_state(blue_score=1, orange_score=0)
    reward_low = reward_func.get_reward(player, state2_low, np.array([0]))
    
    print(f"✓ High shot reward: {reward_high:.4f}")
    print(f"✓ Low shot reward: {reward_low:.4f}")
    print(f"✓ Height multiplier applied: {reward_high > reward_low}")
    
    return reward_high > reward_low

def test_wrong_team_no_reward():
    """Test that players don't get rewarded for opponent goals."""
    print("\n=== Testing Wrong Team No Reward ===")
    
    reward_func = GoalSpeedAndPlacementReward()
    
    initial_state = create_mock_game_state(blue_score=0, orange_score=0)
    reward_func.reset(initial_state)
    
    # Blue player touches ball
    blue_player = create_mock_player(team_num=BLUE_TEAM, car_id=0)
    orange_player = create_mock_player(team_num=ORANGE_TEAM, car_id=1)
    
    state1 = create_mock_game_state(blue_score=0, orange_score=0, ball_velocity=[500, 0, 0])
    blue_player.ball_touched = True
    reward_func.get_reward(blue_player, state1, np.array([0]))
    
    # Orange team scores (own goal scenario)
    state2 = create_mock_game_state(blue_score=0, orange_score=1)
    blue_player.ball_touched = False
    
    blue_reward = reward_func.get_reward(blue_player, state2, np.array([0]))
    orange_reward = reward_func.get_reward(orange_player, state2, np.array([0]))
    
    print(f"✓ Blue player reward (should be 0): {blue_reward}")
    print(f"✓ Orange player reward (should be 0): {orange_reward}")
    
    return blue_reward == 0 and orange_reward == 0

def main():
    """Run all tests."""
    print("GoalSpeedAndPlacementReward Fix Test")
    print("=" * 50)
    
    test1 = test_goal_speed_reward()
    test2 = test_goal_placement_reward()
    test3 = test_wrong_team_no_reward()
    
    print("\n" + "=" * 50)
    if test1 and test2 and test3:
        print("🎉 All tests passed! GoalSpeedAndPlacementReward is fixed.")
        print("\nKey improvements:")
        print("✓ Proper ball velocity tracking before goals")
        print("✓ Correct reward attribution to goal scorer")
        print("✓ Height-based placement bonuses work")
        print("✓ No rewards for wrong team")
        print("✓ Implemented get_final_reward method")
        return True
    else:
        print("❌ Some tests failed. Check the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
