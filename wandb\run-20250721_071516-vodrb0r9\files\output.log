Checkpoint loaded!
<PERSON><PERSON> successfully initialized!
Press (p) to pause (c) to checkpoint, (q) to checkpoint and quit (after next iteration)
C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\torch\optim\adam.py:534: UserWarning: The operator 'aten::lerp.Scalar_out' is not currently supported on the DML backend and will fall back to run on the CPU. This may have performance implications. (Triggered internally at C:\__w\1\s\pytorch-directml-plugin\torch_directml\csrc\dml\dml_cpu_fallback.cpp:17.)
  torch._foreach_lerp_(device_exp_avgs, device_grads, 1 - beta1)

--------BEGIN ITERATION REPORT--------
Policy Reward: 2,893.15157
Policy Entropy: 2.92145
Value Function Loss: 14.72318

Mean KL Divergence: 0.07281
SB3 Clip Fraction: 0.30446
Policy Update Magnitude: 0.65016
Value Function Update Magnitude: 0.39970

Collected Steps per Second: 10,743.44437
Overall Steps per Second: 5,536.92396

Timestep Collection Time: 9.30819
Timestep Consumption Time: 8.75274
PPO Batch Consumption Time: 2.23970
Total Iteration Time: 18.06093

Cumulative Model Updates: 1,386
Cumulative Timesteps: 123,224,125

Timesteps Collected: 100,002
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 2,772.99650
Policy Entropy: 3.00568
Value Function Loss: 9.92379

Mean KL Divergence: 0.06889
SB3 Clip Fraction: 0.46889
Policy Update Magnitude: 0.59480
Value Function Update Magnitude: 0.72116

Collected Steps per Second: 10,697.37829
Overall Steps per Second: 6,709.66247

Timestep Collection Time: 9.35033
Timestep Consumption Time: 5.55713
PPO Batch Consumption Time: 0.92727
Total Iteration Time: 14.90746

Cumulative Model Updates: 1,390
Cumulative Timesteps: 123,324,149

Timesteps Collected: 100,024
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 6,858.26154
Policy Entropy: 3.03736
Value Function Loss: 7.95021

Mean KL Divergence: 0.02580
SB3 Clip Fraction: 0.37716
Policy Update Magnitude: 0.40650
Value Function Update Magnitude: 0.58529

Collected Steps per Second: 10,803.65077
Overall Steps per Second: 6,742.31947

Timestep Collection Time: 9.25965
Timestep Consumption Time: 5.57768
PPO Batch Consumption Time: 0.93180
Total Iteration Time: 14.83733

Cumulative Model Updates: 1,394
Cumulative Timesteps: 123,424,187

Timesteps Collected: 100,038
--------END ITERATION REPORT--------


Saving checkpoint 123424187...
Checkpoint 123424187 saved!
