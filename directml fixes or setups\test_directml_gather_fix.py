#!/usr/bin/env python3
"""
Test script to verify the DirectML gather operation fix.
"""

import torch
import torch_directml
import numpy as np

def test_gather_alternatives():
    """Test that our DirectML-compatible gather alternative works correctly."""
    print("=== Testing DirectML Gather Fix ===")
    
    # Get DirectML device
    device = torch_directml.device()
    print(f"Using device: {device}")
    
    # Create test data
    batch_size = 32
    n_actions = 90  # Typical for LookupAction
    
    # Create probability matrix and actions
    probs = torch.randn(batch_size, n_actions, device=device)
    probs = torch.softmax(probs, dim=-1)
    actions = torch.randint(0, n_actions, (batch_size, 1), device=device)
    
    log_probs = torch.log(probs)
    
    print(f"✓ Created test tensors - batch_size: {batch_size}, n_actions: {n_actions}")
    
    # Test DirectML-compatible method (advanced indexing)
    try:
        batch_indices = torch.arange(batch_size, device=device).unsqueeze(1)
        directml_result = log_probs[batch_indices, actions]
        print("✓ DirectML-compatible advanced indexing works")
        print(f"✓ Result shape: {directml_result.shape}")
    except Exception as e:
        print(f"✗ DirectML-compatible method failed: {e}")
        return False
    
    # Test standard gather method (for comparison, might fail on DirectML)
    try:
        standard_result = log_probs.gather(-1, actions)
        print("✓ Standard gather method works")
        
        # Compare results
        if torch.allclose(directml_result, standard_result, atol=1e-6):
            print("✓ Both methods produce identical results")
        else:
            print("! Methods produce different results (this might be expected)")
            
    except Exception as e:
        print(f"! Standard gather method failed (expected on DirectML): {e}")
    
    # Test backward pass
    try:
        loss = directml_result.sum()
        loss.backward()
        print("✓ Backward pass successful with DirectML-compatible method")
        return True
    except Exception as e:
        print(f"✗ Backward pass failed: {e}")
        return False

def test_discrete_policy():
    """Test the actual DiscreteFF policy with DirectML."""
    print("\n=== Testing DiscreteFF Policy ===")
    
    try:
        from rlgym_ppo.ppo import DiscreteFF
        import torch_directml
        
        device = torch_directml.device()
        
        # Create a simple discrete policy
        input_shape = 107  # Typical observation size
        n_actions = 90     # Typical for LookupAction
        layer_sizes = [256, 256]
        
        policy = DiscreteFF(input_shape, n_actions, layer_sizes, device)
        print(f"✓ DiscreteFF policy created on device: {next(policy.parameters()).device}")
        
        # Test forward pass
        batch_size = 16
        obs = torch.randn(batch_size, input_shape, device=device)
        acts = torch.randint(0, n_actions, (batch_size, 1), device=device)
        
        # Test get_backprop_data (this was causing the scatter error)
        log_probs, entropy = policy.get_backprop_data(obs, acts)
        print(f"✓ get_backprop_data successful - log_probs shape: {log_probs.shape}")
        
        # Test backward pass
        loss = -log_probs.mean() + 0.01 * entropy
        loss.backward()
        print("✓ Backward pass successful - no scatter error!")
        
        return True
        
    except Exception as e:
        print(f"✗ DiscreteFF policy test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    print("DirectML Gather Operation Fix Test")
    print("=" * 50)
    
    success1 = test_gather_alternatives()
    success2 = test_discrete_policy()
    
    if success1 and success2:
        print("\n" + "=" * 50)
        print("🎉 All tests passed! DirectML gather fix is working.")
        print("The scatter error should be resolved.")
        return True
    else:
        print("\n" + "=" * 50)
        print("❌ Some tests failed. Check the output above.")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
