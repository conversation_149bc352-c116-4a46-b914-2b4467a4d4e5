Checkpoint loaded!
<PERSON><PERSON> successfully initialized!
Press (p) to pause (c) to checkpoint, (q) to checkpoint and quit (after next iteration)
C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\torch\optim\adam.py:534: UserWarning: The operator 'aten::lerp.Scalar_out' is not currently supported on the DML backend and will fall back to run on the CPU. This may have performance implications. (Triggered internally at C:\__w\1\s\pytorch-directml-plugin\torch_directml\csrc\dml\dml_cpu_fallback.cpp:17.)
  torch._foreach_lerp_(device_exp_avgs, device_grads, 1 - beta1)

--------BEGIN ITERATION REPORT--------
Policy Reward: 258.51570
Policy Entropy: 4.48595
Value Function Loss: 1.66647

Mean KL Divergence: 0.00001
SB3 Clip Fraction: 0.00000
Policy Update Magnitude: 0.14351
Value Function Update Magnitude: 0.14450

Collected Steps per Second: 11,026.38016
Overall Steps per Second: 7,253.80527

Timestep Collection Time: 4.53494
Timestep Consumption Time: 2.35854
PPO Batch Consumption Time: 0.56069
Total Iteration Time: 6.89349

Cumulative Model Updates: 80
Cumulative Timesteps: 850,276

Timesteps Collected: 50,004
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 212.80311
Policy Entropy: 4.48238
Value Function Loss: 1.47987

Mean KL Divergence: 0.00064
SB3 Clip Fraction: 0.01770
Policy Update Magnitude: 0.32824
Value Function Update Magnitude: 0.23675

Collected Steps per Second: 12,177.81756
Overall Steps per Second: 8,795.59305

Timestep Collection Time: 4.10829
Timestep Consumption Time: 1.57979
PPO Batch Consumption Time: 0.15056
Total Iteration Time: 5.68808

Cumulative Model Updates: 84
Cumulative Timesteps: 900,306

Timesteps Collected: 50,030
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 246.59797
Policy Entropy: 4.48054
Value Function Loss: 1.51173

Mean KL Divergence: 0.00136
SB3 Clip Fraction: 0.05662
Policy Update Magnitude: 0.45311
Value Function Update Magnitude: 0.30895

Collected Steps per Second: 12,287.18140
Overall Steps per Second: 8,429.35874

Timestep Collection Time: 4.07270
Timestep Consumption Time: 1.86393
PPO Batch Consumption Time: 0.15127
Total Iteration Time: 5.93663

Cumulative Model Updates: 90
Cumulative Timesteps: 950,348

Timesteps Collected: 50,042
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 322.28677
Policy Entropy: 4.47913
Value Function Loss: 1.43686

Mean KL Divergence: 0.00128
SB3 Clip Fraction: 0.05532
Policy Update Magnitude: 0.43605
Value Function Update Magnitude: 0.23625

Collected Steps per Second: 12,248.56332
Overall Steps per Second: 8,373.03731

Timestep Collection Time: 4.08423
Timestep Consumption Time: 1.89042
PPO Batch Consumption Time: 0.15383
Total Iteration Time: 5.97465

Cumulative Model Updates: 96
Cumulative Timesteps: 1,000,374

Timesteps Collected: 50,026
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 303.78487
Policy Entropy: 4.47686
Value Function Loss: 1.56000

Mean KL Divergence: 0.00117
SB3 Clip Fraction: 0.04624
Policy Update Magnitude: 0.43880
Value Function Update Magnitude: 0.19744

Collected Steps per Second: 12,266.42956
Overall Steps per Second: 8,387.70836

Timestep Collection Time: 4.07845
Timestep Consumption Time: 1.88599
PPO Batch Consumption Time: 0.15232
Total Iteration Time: 5.96444

Cumulative Model Updates: 102
Cumulative Timesteps: 1,050,402

Timesteps Collected: 50,028
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 244.28639
Policy Entropy: 4.47625
Value Function Loss: 1.49007

Mean KL Divergence: 0.00109
SB3 Clip Fraction: 0.04254
Policy Update Magnitude: 0.43698
Value Function Update Magnitude: 0.21139

Collected Steps per Second: 11,583.65142
Overall Steps per Second: 7,973.66730

Timestep Collection Time: 4.31936
Timestep Consumption Time: 1.95554
PPO Batch Consumption Time: 0.15765
Total Iteration Time: 6.27490

Cumulative Model Updates: 108
Cumulative Timesteps: 1,100,436

Timesteps Collected: 50,034
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 269.10742
Policy Entropy: 4.47243
Value Function Loss: 1.54109

Mean KL Divergence: 0.00122
SB3 Clip Fraction: 0.05403
Policy Update Magnitude: 0.44504
Value Function Update Magnitude: 0.25154

Collected Steps per Second: 10,512.93926
Overall Steps per Second: 7,466.29822

Timestep Collection Time: 4.75814
Timestep Consumption Time: 1.94157
PPO Batch Consumption Time: 0.15279
Total Iteration Time: 6.69971

Cumulative Model Updates: 114
Cumulative Timesteps: 1,150,458

Timesteps Collected: 50,022
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 396.66696
Policy Entropy: 4.47148
Value Function Loss: 1.53720

Mean KL Divergence: 0.00113
SB3 Clip Fraction: 0.04831
Policy Update Magnitude: 0.45239
Value Function Update Magnitude: 0.27456

Collected Steps per Second: 11,342.86843
Overall Steps per Second: 7,767.34518

Timestep Collection Time: 4.40911
Timestep Consumption Time: 2.02964
PPO Batch Consumption Time: 0.15051
Total Iteration Time: 6.43875

Cumulative Model Updates: 120
Cumulative Timesteps: 1,200,470

Timesteps Collected: 50,012
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 265.32405
Policy Entropy: 4.46945
Value Function Loss: 1.55889

Mean KL Divergence: 0.00126
SB3 Clip Fraction: 0.05550
Policy Update Magnitude: 0.45795
Value Function Update Magnitude: 0.31735

Collected Steps per Second: 11,385.66172
Overall Steps per Second: 7,846.61460

Timestep Collection Time: 4.39184
Timestep Consumption Time: 1.98084
PPO Batch Consumption Time: 0.15302
Total Iteration Time: 6.37268

Cumulative Model Updates: 126
Cumulative Timesteps: 1,250,474

Timesteps Collected: 50,004
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 252.28988
Policy Entropy: 4.46656
Value Function Loss: 1.63067

Mean KL Divergence: 0.00127
SB3 Clip Fraction: 0.05596
Policy Update Magnitude: 0.45655
Value Function Update Magnitude: 0.27087

Collected Steps per Second: 11,030.99901
Overall Steps per Second: 7,686.29504

Timestep Collection Time: 4.53431
Timestep Consumption Time: 1.97311
PPO Batch Consumption Time: 0.15497
Total Iteration Time: 6.50743

Cumulative Model Updates: 132
Cumulative Timesteps: 1,300,492

Timesteps Collected: 50,018
--------END ITERATION REPORT--------


--------BEGIN ITERATION REPORT--------
Policy Reward: 284.99011
Policy Entropy: 4.46589
Value Function Loss: 1.64301

Mean KL Divergence: 0.00115
SB3 Clip Fraction: 0.04876
Policy Update Magnitude: 0.45932
Value Function Update Magnitude: 0.31885

Collected Steps per Second: 11,852.44409
Overall Steps per Second: 8,167.06683

Timestep Collection Time: 4.21854
Timestep Consumption Time: 1.90361
PPO Batch Consumption Time: 0.15474
Total Iteration Time: 6.12215

Cumulative Model Updates: 138
Cumulative Timesteps: 1,350,492

Timesteps Collected: 50,000
--------END ITERATION REPORT--------


Saving checkpoint 1350492...
Checkpoint 1350492 saved!
