# DirectML Setup Guide for RLGym PPO

This guide explains how to use DirectML for GPU acceleration with RLGym PPO on Windows.

## What is DirectML?

DirectML is Microsoft's hardware-accelerated machine learning library that provides GPU acceleration across different vendors (NVIDIA, AMD, Intel) without requiring CUDA. It's particularly useful on Windows systems where you want GPU acceleration but don't have CUDA available or want vendor-agnostic acceleration.

## Installation

The DirectML dependencies have been automatically installed:

- **PyTorch**: 2.4.1 (compatible with DirectML)
- **torch-directml**: 0.2.5+ (DirectML backend for PyTorch)

If you need to reinstall manually:

```bash
pip install torch-directml
```

This will automatically install the correct PyTorch version.

## Usage

### Device Options

The Learner class now supports the following device options:

1. **`"directml"`** - Use DirectML for GPU acceleration (recommended for Windows)
2. **`"cuda"`** - Use CUDA for NVIDIA GPUs (if available)
3. **`"cpu"`** - Use CPU only
4. **`"auto"`** - Automatically select the best available device (priority: CUDA > DirectML > CPU)

### Example Usage

```python
from rlgym_ppo import Learner

learner = Learner(
    build_rocketsim_env,
    device="directml",  # Use DirectML
    # ... other parameters
)
```

### Device Selection Priority

When using `device="auto"`, the system will automatically select:

1. **CUDA** (if NVIDIA GPU and CUDA are available)
2. **DirectML** (if DirectML is available)
3. **CPU** (fallback)

## Testing DirectML

Run the included test script to verify DirectML is working:

```bash
python test_directml.py
```

This will test:
- DirectML device detection
- Basic tensor operations
- Neural network training
- Integration with the Learner class

## Performance Considerations

### DirectML vs CUDA vs CPU

- **CUDA**: Best performance on NVIDIA GPUs
- **DirectML**: Good performance on all GPU vendors, vendor-agnostic
- **CPU**: Slowest but most compatible

### Memory Management

DirectML uses a different memory management system than CUDA:
- Cache clearing is handled automatically
- Garbage collection is triggered for DirectML devices
- Memory usage patterns may differ from CUDA

## Troubleshooting

### DirectML Not Available

If you see "DirectML is not available":

1. Ensure you're on Windows 10/11
2. Update your GPU drivers
3. Reinstall torch-directml: `pip install --force-reinstall torch-directml`

### Performance Issues

If DirectML performance is poor:

1. Check GPU utilization in Task Manager
2. Ensure your GPU supports DirectX 12
3. Try different batch sizes
4. Consider using `device="auto"` to let the system choose

### Import Errors

If you get import errors:

1. Ensure torch-directml is installed: `pip list | grep torch`
2. Check PyTorch version compatibility
3. Reinstall dependencies: `pip install -r requirements.txt`

## Example Configuration

Here's the updated example.py configuration:

```python
learner = Learner(
    build_rocketsim_env,
    n_proc=32,
    min_inference_size=max(1, int(round(32 * 0.9))),
    metrics_logger=metrics_logger,
    ppo_batch_size=50_000,
    ts_per_iteration=50_000,
    exp_buffer_size=150_000,
    ppo_minibatch_size=25_000,
    ppo_ent_coef=0.01,
    ppo_epochs=2,
    standardize_returns=True,
    standardize_obs=False,
    save_every_ts=12_000_000,
    timestep_limit=100_000_000_000,
    log_to_wandb=True,
    policy_layer_sizes=(1024, 512, 512),
    critic_layer_sizes=(1024, 512, 512),
    device="directml",  # DirectML for GPU acceleration
    render=True,
    render_delay=0.04,
    policy_lr=2e-4,
    critic_lr=2e-4,
    wandb_run_name="Training",
)
```

## System Requirements

- **OS**: Windows 10/11
- **GPU**: Any DirectX 12 compatible GPU (NVIDIA, AMD, Intel)
- **Python**: 3.8+
- **PyTorch**: 2.4.1+
- **torch-directml**: 0.2.5+

## Additional Resources

- [DirectML Documentation](https://docs.microsoft.com/en-us/windows/ai/directml/)
- [PyTorch DirectML GitHub](https://github.com/pytorch/directml)
- [RLGym Documentation](https://rlgym.github.io/)

## Support

If you encounter issues with DirectML:

1. Run `python test_directml.py` to diagnose problems
2. Check the console output for specific error messages
3. Verify your system meets the requirements above
4. Consider using `device="auto"` as a fallback option
