#!/usr/bin/env python3
"""
DirectML Test Script
Tests DirectML device detection and basic tensor operations.
"""

import torch
import numpy as np

def test_directml_availability():
    """Test if DirectML is available and can be initialized."""
    print("=== DirectML Availability Test ===")
    
    try:
        import torch_directml
        print("✓ torch-directml package is installed")
        
        # Get DirectML device
        device = torch_directml.device()
        print(f"✓ DirectML device initialized: {device}")
        
        # Test device properties
        print(f"✓ Device type: {device.type}")
        print(f"✓ Device index: {device.index}")
        
        return device
    except ImportError:
        print("✗ torch-directml package not found")
        return None
    except Exception as e:
        print(f"✗ DirectML initialization failed: {e}")
        return None

def test_basic_tensor_operations(device):
    """Test basic tensor operations on DirectML device."""
    print("\n=== Basic Tensor Operations Test ===")
    
    try:
        # Create tensors on DirectML device
        x = torch.randn(100, 50, device=device)
        y = torch.randn(50, 25, device=device)
        
        print(f"✓ Created tensor x on device: {x.device}")
        print(f"✓ Created tensor y on device: {y.device}")
        
        # Matrix multiplication
        z = torch.mm(x, y)
        print(f"✓ Matrix multiplication successful, result device: {z.device}")
        print(f"✓ Result shape: {z.shape}")
        
        # Move to CPU and back
        z_cpu = z.cpu()
        z_back = z_cpu.to(device)
        print(f"✓ CPU transfer successful")
        print(f"✓ Back to DirectML successful: {z_back.device}")
        
        return True
    except Exception as e:
        print(f"✗ Tensor operations failed: {e}")
        return False

def test_neural_network(device):
    """Test a simple neural network on DirectML device."""
    print("\n=== Neural Network Test ===")
    
    try:
        # Create a simple neural network
        model = torch.nn.Sequential(
            torch.nn.Linear(10, 64),
            torch.nn.ReLU(),
            torch.nn.Linear(64, 32),
            torch.nn.ReLU(),
            torch.nn.Linear(32, 1)
        ).to(device)
        
        print(f"✓ Neural network created and moved to device: {next(model.parameters()).device}")
        
        # Create input data
        input_data = torch.randn(32, 10, device=device)
        print(f"✓ Input data created on device: {input_data.device}")
        
        # Forward pass
        output = model(input_data)
        print(f"✓ Forward pass successful, output shape: {output.shape}")
        print(f"✓ Output device: {output.device}")
        
        # Backward pass
        loss = torch.nn.functional.mse_loss(output, torch.randn_like(output))
        loss.backward()
        print(f"✓ Backward pass successful, loss: {loss.item():.4f}")
        
        return True
    except Exception as e:
        print(f"✗ Neural network test failed: {e}")
        return False

def test_learner_device_selection():
    """Test the Learner class device selection logic."""
    print("\n=== Learner Device Selection Test ===")
    
    try:
        # Import the Learner class
        from rlgym_ppo import Learner
        
        # Test DirectML device selection
        print("Testing DirectML device selection...")
        
        # We can't fully initialize the Learner without an environment function,
        # but we can test the device selection logic by creating a minimal test
        
        # Test device selection logic directly
        import torch_directml
        device = torch_directml.device()
        print(f"✓ DirectML device can be created: {device}")
        
        # Test string representation for cache clearing logic
        device_str = str(device)
        print(f"✓ Device string representation: {device_str}")
        
        if "privateuseone" in device_str:
            print("✓ DirectML device uses privateuseone backend as expected")
        else:
            print(f"! DirectML device backend: {device_str}")
        
        return True
    except Exception as e:
        print(f"✗ Learner device selection test failed: {e}")
        return False

def main():
    """Main test function."""
    print("DirectML Integration Test")
    print("=" * 50)
    
    # Test DirectML availability
    device = test_directml_availability()
    if device is None:
        print("\n❌ DirectML is not available. Please install torch-directml.")
        return False
    
    # Test basic operations
    if not test_basic_tensor_operations(device):
        print("\n❌ Basic tensor operations failed.")
        return False
    
    # Test neural network
    if not test_neural_network(device):
        print("\n❌ Neural network test failed.")
        return False
    
    # Test learner integration
    if not test_learner_device_selection():
        print("\n❌ Learner integration test failed.")
        return False
    
    print("\n" + "=" * 50)
    print("🎉 All DirectML tests passed successfully!")
    print("DirectML is ready to use with rlgym-ppo.")
    return True

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
