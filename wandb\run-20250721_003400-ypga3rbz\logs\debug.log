2025-07-21 00:34:00,342 INFO    MainThread:26264 [wandb_setup.py:_flush():80] Current SDK version is 0.21.0
2025-07-21 00:34:00,342 INFO    MainThread:26264 [wandb_setup.py:_flush():80] Configure stats pid to 26264
2025-07-21 00:34:00,342 INFO    MainThread:26264 [wandb_setup.py:_flush():80] Loading settings from C:\Users\<USER>\.config\wandb\settings
2025-07-21 00:34:00,342 INFO    MainThread:26264 [wandb_setup.py:_flush():80] Loading settings from C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\wandb\settings
2025-07-21 00:34:00,342 INFO    MainThread:26264 [wandb_setup.py:_flush():80] Loading settings from environment variables
2025-07-21 00:34:00,342 INFO    MainThread:26264 [wandb_init.py:setup_run_log_directory():703] Logging user logs to C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\wandb\run-20250721_003400-ypga3rbz\logs\debug.log
2025-07-21 00:34:00,342 INFO    MainThread:26264 [wandb_init.py:setup_run_log_directory():704] Logging internal logs to C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\wandb\run-20250721_003400-ypga3rbz\logs\debug-internal.log
2025-07-21 00:34:00,342 INFO    MainThread:26264 [wandb_init.py:init():830] calling init triggers
2025-07-21 00:34:00,344 INFO    MainThread:26264 [wandb_init.py:init():835] wandb.init called with sweep_config: {}
config: {'n_proc': 32, 'min_inference_size': 29, 'timestep_limit': 100000000000, 'exp_buffer_size': 150000, 'ts_per_iteration': 50000, 'standardize_returns': True, 'standardize_obs': False, 'policy_layer_sizes': [1024, 512, 512], 'critic_layer_sizes': [1024, 512, 512], 'ppo_epochs': 2, 'ppo_batch_size': 50000, 'ppo_minibatch_size': 25000, 'ppo_ent_coef': 0.01, 'ppo_clip_range': 0.1, 'gae_lambda': 0.95, 'gae_gamma': 0.99, 'policy_lr': 0.0002, 'critic_lr': 0.0002, 'shm_buffer_size': 8192, '_wandb': {}}
2025-07-21 00:34:00,344 INFO    MainThread:26264 [wandb_init.py:init():871] starting backend
2025-07-21 00:34:00,565 INFO    MainThread:26264 [wandb_init.py:init():874] sending inform_init request
2025-07-21 00:34:00,575 INFO    MainThread:26264 [wandb_init.py:init():882] backend started and connected
2025-07-21 00:34:00,577 INFO    MainThread:26264 [wandb_init.py:init():953] updated telemetry
2025-07-21 00:34:00,578 INFO    MainThread:26264 [wandb_init.py:init():977] communicating run to backend with 90.0 second timeout
2025-07-21 00:34:01,232 INFO    MainThread:26264 [wandb_init.py:init():1024] run resumed
2025-07-21 00:34:01,233 INFO    MainThread:26264 [wandb_init.py:init():1029] starting run threads in backend
2025-07-21 00:34:01,287 INFO    MainThread:26264 [wandb_run.py:_console_start():2458] atexit reg
2025-07-21 00:34:01,287 INFO    MainThread:26264 [wandb_run.py:_redirect():2306] redirect: wrap_raw
2025-07-21 00:34:01,287 INFO    MainThread:26264 [wandb_run.py:_redirect():2375] Wrapping output streams.
2025-07-21 00:34:01,287 INFO    MainThread:26264 [wandb_run.py:_redirect():2398] Redirects installed.
2025-07-21 00:34:01,290 INFO    MainThread:26264 [wandb_init.py:init():1075] run started, returning control to user process
2025-07-21 00:35:31,987 WARNING MsgRouterThr:26264 [router.py:message_loop():63] [no run ID] message_loop has been closed
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 204, in _read_packet_bytes
    data = self._sock.recv(self._bufsize)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\router_sock.py", line 27, in _read_message
    return self._sock_client.read_server_response(timeout=1)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 231, in read_server_response
    data = self._read_packet_bytes(timeout=timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 208, in _read_packet_bytes
    raise SockClientClosedError from e
wandb.sdk.lib.sock_client.SockClientClosedError

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\router.py", line 56, in message_loop
    msg = self._read_message()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\router_sock.py", line 29, in _read_message
    raise MessageRouterClosedError from e
wandb.sdk.interface.router.MessageRouterClosedError
2025-07-21 00:35:32,006 INFO    MsgRouterThr:26264 [mailbox.py:close():129] [no run ID] Closing mailbox, abandoning 1 handles.
2025-07-21 00:35:37,396 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stdout callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 224, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 293, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2379, in <lambda>
    lambda data: self._console_raw_callback("stdout", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,453 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stdout callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 224, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 293, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2379, in <lambda>
    lambda data: self._console_raw_callback("stdout", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,455 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 224, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 293, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,455 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 224, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 293, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,457 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 224, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 293, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,458 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 224, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 293, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,460 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 224, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 293, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,461 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 224, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 293, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,462 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 224, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 293, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,463 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 224, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 293, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,464 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 224, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 293, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,466 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 224, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 293, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,466 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 224, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 293, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,468 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 224, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 293, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,469 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 224, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 293, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,470 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 224, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 293, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,471 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 224, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 293, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,473 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 224, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 293, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,475 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 224, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 293, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,477 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 224, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 293, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,477 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 224, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 293, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,478 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 224, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 293, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,480 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 224, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 293, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,481 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 224, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 293, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,481 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 224, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 293, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,483 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 224, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 293, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,485 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 224, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 293, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,486 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 224, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 293, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,486 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 224, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 293, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,488 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 224, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 293, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,491 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 224, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 293, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,492 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 224, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 293, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,493 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 224, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 293, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,495 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 224, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 293, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,495 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 224, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 293, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,496 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 224, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 293, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,498 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 224, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 293, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,499 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 224, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 293, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,499 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 224, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 293, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,501 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 224, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 293, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,503 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stdout callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 224, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 293, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2379, in <lambda>
    lambda data: self._console_raw_callback("stdout", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,504 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stdout callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 224, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 293, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2379, in <lambda>
    lambda data: self._console_raw_callback("stdout", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,531 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stdout callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 224, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 293, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2379, in <lambda>
    lambda data: self._console_raw_callback("stdout", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,533 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stdout callback
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 224, in learn
    self._learn()
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\learner.py", line 293, in _learn
    reporting.report_metrics(loggable_metrics=report,
  File "C:\Users\<USER>\Downloads\rlgymPPO\rlgymPPO\Rlgym\rlgym_ppo\util\reporting.py", line 59, in report_metrics
    wandb_run.log(loggable_metrics)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 443, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1987, in log
    self._log(data=data, step=step, commit=commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1710, in _log
    self._partial_history_callback(data, step, commit)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1537, in _partial_history_callback
    self._backend.interface.publish_partial_history(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 700, in publish_partial_history
    self._publish_partial_history(partial_history)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 48, in _publish_partial_history
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2379, in <lambda>
    lambda data: self._console_raw_callback("stdout", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,546 INFO    MainThread:26264 [wandb_run.py:_finish():2224] finishing run xzskullyxz-rlgym/rlgym-ppo/ypga3rbz
2025-07-21 00:35:37,548 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,549 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,549 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,551 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,551 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,552 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,553 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,553 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,553 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,554 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,555 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,555 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,555 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,557 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,557 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,558 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,558 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,559 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,560 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,560 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,561 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,561 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,561 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,563 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,563 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,564 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,565 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,566 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,566 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,566 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,567 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,567 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,569 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,569 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,570 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,570 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,571 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,571 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,571 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,573 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,573 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,574 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,574 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,576 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,576 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,577 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,577 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,579 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,579 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,579 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,580 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-21 00:35:37,580 ERROR   MainThread:26264 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\redirect.py", line 662, in _on_write
    cb(written_data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\interface\interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\wandb\sdk\lib\sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
