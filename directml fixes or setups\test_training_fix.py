#!/usr/bin/env python3
"""
Test script to verify DirectML training works without scatter errors.
"""

from rlgym_ppo.ppo import DiscreteFF
import torch_directml
import torch

def test_training():
    print('Testing DirectML DiscreteFF policy training...')

    device = torch_directml.device()
    print(f'Device: {device}')

    # Create policy
    policy = DiscreteFF(107, 90, [256, 256], device)
    optimizer = torch.optim.Adam(policy.parameters(), lr=1e-4)

    # Create training data
    batch_size = 32
    obs = torch.randn(batch_size, 107, device=device, requires_grad=False)
    acts = torch.randint(0, 90, (batch_size, 1), device=device)

    # Forward pass
    log_probs, entropy = policy.get_backprop_data(obs, acts)
    print(f'Forward pass successful')

    # Compute loss
    loss = -log_probs.mean() + 0.01 * entropy
    print(f'Loss computed: {loss.item():.4f}')

    # Backward pass
    optimizer.zero_grad()
    loss.backward()
    print(f'Backward pass successful - no scatter error!')

    # Update parameters
    optimizer.step()
    print(f'Parameter update successful')

    print('DirectML training test passed!')
    return True

if __name__ == "__main__":
    try:
        test_training()
        print("SUCCESS: Fix is working!")
    except Exception as e:
        print(f"FAILED: {e}")
        import traceback
        traceback.print_exc()
